import { WifiOff } from '@assets/images';
import { useCharge } from '@bp/charge-mfe';
import { SimpleNotification } from '@bp/ui-components/mobile/core';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useConnectivity } from '../../providers/ConnectivityProvider';

const Notifications = () => {
  const { isCharging: isChargingHook } = useCharge();
  const { isInternetReachable } = useConnectivity();
  const { t } = useTranslation();

  const [isConnected, setIsConnected] = useState(true);
  const [isCharging, setIsCharging] = useState(false);

  useEffect(() => {
    if (isChargingHook !== isCharging) {
      setIsCharging(isChargingHook);
    }

    if (isInternetReachable !== isConnected) {
      setIsConnected(isInternetReachable);
    }
  }, [isInternetReachable, isChargingHook, isCharging, isConnected]);

  // Show connection lost notification when not connected and not charging
  if (!isConnected && !isCharging) {
    return (
      <SimpleNotification
        title={t('notifications.lostConnection.title')}
        icon={<WifiOff />}
      />
    );
  }

  // Show connection lost notification when not connected but charging
  if (!isConnected && isCharging) {
    return (
      <SimpleNotification
        icon={<WifiOff />}
        title={t('notifications.lostConnection.title')}
        text={t('notifications.lostConnection.text')}
      />
    );
  }

  return null;
};

export default Notifications;
